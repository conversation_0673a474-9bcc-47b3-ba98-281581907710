{"version": 3, "names": ["_isNode", "require", "assertNode", "node", "isNode", "_node$type", "type", "JSON", "stringify", "TypeError"], "sources": ["../../src/asserts/assertNode.ts"], "sourcesContent": ["import isNode from \"../validators/isNode.ts\";\nimport type * as t from \"../index.ts\";\n\nexport default function assertNode(node?: any): asserts node is t.Node {\n  if (!isNode(node)) {\n    const type = node?.type ?? JSON.stringify(node);\n    throw new TypeError(`Not a valid node of type \"${type}\"`);\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AAGe,SAASC,UAAUA,CAACC,IAAU,EAA0B;EACrE,IAAI,CAAC,IAAAC,eAAM,EAACD,IAAI,CAAC,EAAE;IAAA,IAAAE,UAAA;IACjB,MAAMC,IAAI,IAAAD,UAAA,GAAGF,IAAI,oBAAJA,IAAI,CAAEG,IAAI,YAAAD,UAAA,GAAIE,IAAI,CAACC,SAAS,CAACL,IAAI,CAAC;IAC/C,MAAM,IAAIM,SAAS,CAAC,6BAA6BH,IAAI,GAAG,CAAC;EAC3D;AACF", "ignoreList": []}